<?php

namespace App\Http\Controllers;
use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\Header;

class SettingController extends Controller
{
    public function index()
    {
        $headers = Header::all();
        return Inertia::render('setting',[
            'headers' => $headers,
        ]);
    }

    public function addHeader(Request $request)
    {
        $request->validate([
            'image' => 'nullable|image|max:2048', // boleh kosong, jika ingin opsional
            'deskripsi' => 'required|string',
            'title' => 'required|string'
        ]);

        $imagePath = null;

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('header', 'public'); // perbaikan di sini
        }

        Header::create([
            'title_header' => $request->input('title'),
            'header_images' => $imagePath, // simpan sebagai string (pastikan kolom bukan JSON)
            'header_deskripsi' => $request->input('deskripsi')
        ]);

        return redirect()->back()->with('success', 'Header berhasil ditambahkan!');
    }
}
