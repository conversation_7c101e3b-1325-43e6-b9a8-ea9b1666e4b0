import { useState } from 'react';
import { usePage, router } from '@inertiajs/react';
import { motion } from 'framer-motion';
import {
    ArrowLeft,
    Calendar,
    User,
    Clock,
    Share2,
    Share,
    MessageCircle,
    Globe,
    Copy,
    Check
} from 'lucide-react';

interface BeritaData {
    id: number;
    judul: string;
    deskripsi: string;
    tanggal: string;
    gambar: string;
    author: string;
    created_at: string;
    updated_at: string;
}

interface PageProps {
    berita: BeritaData;
    [key: string]: any;
}

export default function PageBerita() {
    const { props } = usePage<PageProps>();
    const { berita } = props;
    const [copied, setCopied] = useState(false);

    // Function to go back to landing page
    const handleGoBack = () => {
        router.visit('/');
    };

    // Function to share article
    const handleShare = async (platform?: string) => {
        const url = window.location.href;
        const title = berita.judul;
        const text = `Baca berita: ${title}`;

        if (platform === 'facebook') {
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
        } else if (platform === 'twitter') {
            window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`, '_blank');
        } else if (platform === 'linkedin') {
            window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`, '_blank');
        } else if (platform === 'copy') {
            try {
                await navigator.clipboard.writeText(url);
                setCopied(true);
                setTimeout(() => setCopied(false), 2000);
            } catch (err) {
                console.error('Failed to copy: ', err);
            }
        }
    };

    // Format content with line breaks
    const formatContent = (content: string) => {
        return content.split('\n').map((paragraph, index) => (
            <p key={index} className="mb-4 text-gray-700 dark:text-gray-300 leading-relaxed">
                {paragraph}
            </p>
        ));
    };

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            {/* Header Navigation */}
            <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
                <div className="max-w-7xl mx-auto px-4 py-4">
                    <div className="flex items-center justify-between">
                        <button
                            onClick={handleGoBack}
                            className="flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 transition-colors duration-200"
                        >
                            <ArrowLeft className="w-5 h-5" />
                            <span className="font-medium">Kembali ke Beranda</span>
                        </button>

                        <div className="flex items-center gap-3">
                            <span className="text-sm text-gray-500 dark:text-gray-400">Bagikan:</span>
                            <div className="flex items-center gap-2">
                                <button
                                    onClick={() => handleShare('facebook')}
                                    className="p-2 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-full transition-colors duration-200"
                                    title="Share to Facebook"
                                >
                                    <Share className="w-4 h-4" />
                                </button>
                                <button
                                    onClick={() => handleShare('twitter')}
                                    className="p-2 text-sky-500 hover:bg-sky-50 dark:hover:bg-sky-900/20 rounded-full transition-colors duration-200"
                                    title="Share to Twitter"
                                >
                                    <MessageCircle className="w-4 h-4" />
                                </button>
                                <button
                                    onClick={() => handleShare('linkedin')}
                                    className="p-2 text-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-full transition-colors duration-200"
                                    title="Share to LinkedIn"
                                >
                                    <Globe className="w-4 h-4" />
                                </button>
                                <button
                                    onClick={() => handleShare('copy')}
                                    className="p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors duration-200"
                                    title="Copy Link"
                                >
                                    {copied ? <Check className="w-4 h-4 text-green-600" /> : <Copy className="w-4 h-4" />}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="max-w-4xl mx-auto px-4 py-8">
                <motion.article
                    className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                >
                    {/* Article Header */}
                    <div className="relative">
                        <img
                            src={berita.gambar}
                            alt={berita.judul}
                            className="w-full h-64 md:h-96 object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                        <div className="absolute bottom-6 left-6 right-6">
                            <motion.h1
                                className="text-2xl md:text-4xl font-bold text-white mb-4 leading-tight"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.2 }}
                            >
                                {berita.judul}
                            </motion.h1>
                        </div>
                    </div>

                    {/* Article Meta */}
                    <div className="p-6 md:p-8">
                        <motion.div
                            className="flex flex-wrap items-center gap-4 mb-8 pb-6 border-b border-gray-200 dark:border-gray-700"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.3 }}
                        >
                            <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                                <Calendar className="w-4 h-4" />
                                <span className="text-sm">{berita.tanggal}</span>
                            </div>
                            <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                                <User className="w-4 h-4" />
                                <span className="text-sm">Oleh {berita.author}</span>
                            </div>
                            <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                                <Clock className="w-4 h-4" />
                                <span className="text-sm">
                                    {Math.ceil(berita.deskripsi.split(' ').length / 200)} menit baca
                                </span>
                            </div>
                        </motion.div>

                        {/* Article Content */}
                        <motion.div
                            className="prose prose-lg max-w-none dark:prose-invert"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.4 }}
                        >
                            <div className="text-gray-800 dark:text-gray-200 leading-relaxed">
                                {formatContent(berita.deskripsi)}
                            </div>
                        </motion.div>

                        {/* Article Footer */}
                        <motion.div
                            className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.5 }}
                        >
                            <div className="flex items-center justify-between">
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                    Dipublikasikan pada {berita.tanggal}
                                </div>
                                <div className="flex items-center gap-2">
                                    <Share2 className="w-4 h-4 text-gray-400" />
                                    <span className="text-sm text-gray-500 dark:text-gray-400">
                                        Bagikan artikel ini
                                    </span>
                                </div>
                            </div>
                        </motion.div>
                    </div>
                </motion.article>

                {/* Back to Home Button */}
                <motion.div
                    className="text-center mt-8"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.6, delay: 0.6 }}
                >
                    <button
                        onClick={handleGoBack}
                        className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2"
                    >
                        Kembali ke Beranda
                    </button>
                </motion.div>
            </main>
        </div>
    );
}